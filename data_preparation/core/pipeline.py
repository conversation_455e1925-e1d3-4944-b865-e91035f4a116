import argparse
import json
import sys
import os
from typing import List, Optional, Dict, Any

from data_preparation.core.config.base import Config<PERSON>oader, Config
from data_preparation.core.provider import LLMviaOpenAI
from data_preparation.core.prompt.default_prompt import DEFAULT_PROMPT
from data_preparation.core.utils import load_txt


import json
import os
from typing import List, Optional, Dict, Any

from data_preparation.core.config.base import ConfigLoader, Config
from data_preparation.core.provider import LL<PERSON>viaOpenAI
from data_preparation.core.prompt.default_prompt import DEFAULT_PROMPT
from data_preparation.core.utils import load_txt

from rainbowplus.api.models import RainbowAnalysisJob
from rainbowplus.api.database import get_db_session

class Pipeline:
    """Pipeline for data preparation tasks.
    
    This class handles the entire pipeline from loading configuration to generating output
    using the specified LLM provider and saving results to the database.
    """
    def __init__(self, config: Config, db_session_factory=get_db_session):
        """Initialize the pipeline with a configuration and database session factory.
        
        Args:
            config: The configuration object containing all necessary settings.
            db_session_factory: A callable that provides a database session context manager.
        """
        self.config = config
        self.llm = LLMviaOpenAI(config.llm)
        self.db_session_factory = db_session_factory
        
        # Load content from files
        base_path = os.path.dirname(os.path.abspath(self.config._file_path)) if hasattr(self.config, '_file_path') else ''
        # Normalize paths to avoid duplicated segments
        def normalize_path(path):
            # Remove leading './' if present to avoid duplication
            if path.startswith('./'):
                path = path[2:]
            # Remove duplicated 'recipes/' segment if present
            parts = path.split(os.sep)
            if len(parts) > 1 and parts[0] == 'recipes' and parts[1] == 'recipes':
                path = os.sep.join(parts[1:])
            return os.path.normpath(os.path.join(base_path, path))
        self.description = load_txt(normalize_path(config.description))
        self.examples = [load_txt(normalize_path(example)) for example in config.examples]
        self.documents = [load_txt(normalize_path(document)) for document in config.documents]
        
        # Format the prompt
        self.prompt = DEFAULT_PROMPT.format(
            description=self.description,
            examples="\n\n".join(self.examples),
            documents="\n\n".join(self.documents),
        )
    
    def generate(self, output_path: Optional[str] = None, job_id: Optional[str] = None) -> Dict[str, Any]:
        """Generate output based on the prompt and task type.
        
        Args:
            output_path: Optional path to save the generated output to a file (deprecated).
            job_id: Optional job ID to update the database record with results.
            
        Returns:
            The generated samples as a dictionary.
        """
        task = self.config.task
        
        # Generate samples based on task type
        if task == "Question-Answering":
            from data_preparation.core.io import QAOutput
            samples = self.llm.generate_format(self.prompt, self.config.llm.sampling_params, QAOutput)
        else:
            from data_preparation.core.io import ContentOutput
            samples = self.llm.generate_format(self.prompt, self.config.llm.sampling_params, ContentOutput)
        
        # Convert samples to dict for storage
        if isinstance(samples, list):
            samples_dict = [sample.model_dump() for sample in samples]
        else:
            samples_dict = samples.model_dump()
        
        # Save samples to database if job_id is provided
        if job_id:
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"🔍 DEBUG: Calling _save_generated_dataset_to_db with job_id: {job_id}")
            self._save_generated_dataset_to_db(job_id, samples_dict)
            logger.info(f"✅ DEBUG: _save_generated_dataset_to_db completed for job_id: {job_id}")
        # Otherwise, fallback to saving to file if output_path is provided
        elif output_path:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            with open(output_path, "w") as f:
                json.dump(samples_dict, f, indent=4)
        
        return samples_dict

    def _save_generated_dataset_to_db(self, job_id: str, samples_dict: dict):
        """
        Save generated dataset to the new GeneratedDataset table and individual samples to DatasetSample table.

        Args:
            job_id: The job ID associated with this generation
            samples_dict: The generated dataset content
        """
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"🔍 DEBUG: _save_generated_dataset_to_db called with job_id: {job_id}")
        logger.info(f"🔍 DEBUG: samples_dict type: {type(samples_dict)}, size: {len(str(samples_dict))}")

        from rainbowplus.api.models import RainbowAnalysisJob, GeneratedDataset, DatagenEvent, DatasetSample
        from datetime import datetime, timezone
        import uuid

        with self.db_session_factory() as session:
            # Get the job to find associated project and update status
            job = session.query(RainbowAnalysisJob).filter(RainbowAnalysisJob.id == job_id).first()
            if not job:
                raise ValueError(f"Job with id {job_id} not found in database.")

            # Update job status to completed (keep for backward compatibility)
            job.status = 'completed'
            session.add(job)

            # Find the most recent datagen event for this project/job
            # This is a best-effort approach to link the dataset to the datagen event
            datagen_event = None
            if job.project_id:
                # Try to find the most recent datagen event for this project
                datagen_event = session.query(DatagenEvent)\
                    .filter(DatagenEvent.project_id == job.project_id)\
                    .order_by(DatagenEvent.created_at.desc())\
                    .first()

            # Calculate dataset statistics
            total_samples = 0
            sample_groups = len(samples_dict) if isinstance(samples_dict, list) else 1

            if isinstance(samples_dict, list):
                for group in samples_dict:
                    if isinstance(group, dict) and 'samples' in group:
                        total_samples += len(group['samples'])

            # Create GeneratedDataset record (keep for backward compatibility)
            generated_dataset = GeneratedDataset(
                id=uuid.uuid4(),
                datagen_event_id=datagen_event.id if datagen_event else None,
                project_id=job.project_id,
                job_id=job.id,
                dataset_name=f"Generated Dataset {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M')}",
                generation_status='completed',
                dataset_content=samples_dict,
                dataset_size=datagen_event.dataset_size if datagen_event else None,
                complexity=datagen_event.complexity if datagen_event else None,
                coverage=datagen_event.coverage if datagen_event else None,
                total_samples=total_samples,
                sample_groups=sample_groups,
                completed_at=datetime.now(timezone.utc)
            )

            session.add(generated_dataset)
            session.flush()  # Flush to get the generated_dataset.id

            # Now save each individual sample as a separate record
            sample_index = 0
            if isinstance(samples_dict, list):
                for group_index, group in enumerate(samples_dict):
                    if isinstance(group, dict) and 'samples' in group:
                        for sample in group['samples']:
                            if isinstance(sample, dict):
                                # Create individual DatasetSample record
                                dataset_sample = DatasetSample(
                                    id=uuid.uuid4(),
                                    generated_dataset_id=generated_dataset.id,
                                    datagen_event_id=datagen_event.id if datagen_event else None,
                                    project_id=job.project_id,
                                    job_id=job.id,
                                    sample_type=sample.get('type', 'content'),
                                    content=sample.get('content', ''),
                                    label=sample.get('label', None),
                                    sample_group=group_index,
                                    sample_index=sample_index
                                )
                                session.add(dataset_sample)
                                sample_index += 1

            session.commit()
            logger.info(f"✅ DEBUG: GeneratedDataset saved successfully with ID: {generated_dataset.id}")
            logger.info(f"✅ DEBUG: Total samples: {total_samples}, Sample groups: {sample_groups}")
            logger.info(f"✅ DEBUG: Individual samples saved: {sample_index}")
            logger.info(f"✅ DEBUG: Linked to datagen_event: {datagen_event.id if datagen_event else 'None'}")

    @classmethod
    def from_config_path(cls, config_path: str) -> 'Pipeline':
        """Create a Pipeline instance from a configuration file path.
        
        Args:
            config_path: Path to the YAML configuration file.
            
        Returns:
            A new Pipeline instance.
        """
        config = ConfigLoader.load(config_path)
        return cls(config)


def parse_arguments() -> argparse.Namespace:
    """Parse command line arguments.
    
    Returns:
        Parsed command line arguments.
    """
    parser = argparse.ArgumentParser(description="Data preparation pipeline")
    parser.add_argument(
        "--config", 
        type=str, 
        default="./recipes/base.yml",
        help="Path to the configuration YAML file"
    )
    parser.add_argument(
        "--output", 
        type=str, 
        default="./outputs/samples.json",
        help="Path to save the generated samples"
    )
    return parser.parse_args()


def main() -> None:
    """Main entry point for the pipeline."""
    args = parse_arguments()
    
    try:
        # Create pipeline from config
        pipeline = Pipeline.from_config_path(args.config)
        
        # Generate output
        samples = pipeline.generate(args.output)
        print(f"Generated samples saved to {args.output}")
        
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()