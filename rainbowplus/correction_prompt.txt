When working on RainbowPlus project:
1. Use rbp environment (source rbp/bin/activate)
2. PostgreSQL (5432) and Redis (6379) are already running
3. Check .env.local for configuration
4. Don't modify Streamlit (user doesn't use it anymore)
5. Use constructor-based dependency injection for new backend features
6. Use package managers, not manual file editing
7. When token limits fail, check original base.yml settings first
8. Database schema changes require server restart
9. Always investigate why existing code works before changing it