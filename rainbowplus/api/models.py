from typing import List, Optional, Dict, Any

from pydantic import BaseModel


class RainbowPlusRequest(BaseModel):
    prompts: list[str]
    target_llm: str
    num_samples: int = 5
    num_mutations: int = 3
    max_iters: int = 1
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    nickname: Optional[str] = None  # User nickname for Slack notifications
    project_id: Optional[str] = None  # Optional project association

class PointResponse(BaseModel):
    adv_prompt: str
    response: str
    score: float
    descriptor: str


class RainbowPlusResponse(BaseModel):
    points: List[PointResponse]


# Project-related Pydantic models
class ProjectCreateRequest(BaseModel):
    name: str
    description: str
    domain: str

class ProjectResponse(BaseModel):
    id: str
    name: str
    description: str
    domain: str
    created_at: str
    updated_at: str

class ProjectListResponse(BaseModel):
    projects: List[ProjectResponse]

# Enhanced project response for dashboard with computed statistics
class ProjectDashboardResponse(BaseModel):
    id: str
    name: str
    description: str
    domain: str
    status: str  # active, completed, pending
    datasetsGenerated: int  # Total number of prompts across all jobs
    testsRun: int  # Total number of completed jobs
    riskScore: float  # Average risk score from job results
    progress: int  # Progress percentage (0-100)
    created_at: str
    updated_at: str

class RecentProjectsResponse(BaseModel):
    projects: List[ProjectDashboardResponse]


class LLMConfigRequest(BaseModel):
    """LLM configuration matching base.yml structure."""
    provider: Optional[str] = "openai"
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    model_kwargs: Optional[Dict[str, Any]] = None
    sampling_params: Optional[Dict[str, Any]] = None


class DatasetGenerationRequest(BaseModel):
    """Request model for dataset generation matching base.yml structure."""
    # Core fields matching base.yml
    description: Optional[str] = None  # Application description from client form
    examples: Optional[List[str]] = None  # Example inputs from client form
    documents: Optional[List[str]] = None  # Will fallback to police-report-system.txt
    task: Optional[str] = "Content"  # Default task type
    llm: Optional[LLMConfigRequest] = None  # LLM configuration

    # Additional fields for job tracking
    nickname: Optional[str] = None
    project_id: Optional[str] = None


class DatasetGenerationResponse(BaseModel):
    """Response model for dataset generation."""
    job_id: str
    status: str
    message: str


class TestTypeSelection(BaseModel):
    """Model for test type selection."""
    id: str
    label: str
    checked: bool


class DatagenEventRequest(BaseModel):
    """Request model for capturing dataset generation form data at step 3 completion."""
    # Form data fields
    application_description: Optional[str] = None
    domain: Optional[str] = None
    example_input: Optional[str] = None
    test_types: Optional[List[TestTypeSelection]] = None
    complexity: Optional[int] = None  # Single value from slider
    coverage: Optional[int] = None    # Single value from slider
    dataset_size: Optional[int] = None  # Single value from slider
    selected_experts: Optional[List[str]] = None
    uploaded_files: Optional[List[str]] = None  # File references/paths

    # Additional metadata
    user_session_id: Optional[str] = None
    project_id: Optional[str] = None


class DatagenEventResponse(BaseModel):
    """Response model for datagen event creation."""
    event_id: str
    status: str
    message: str

class ProjectDashboardListResponse(BaseModel):
    projects: List[ProjectDashboardResponse]


class GeneratedDatasetResponse(BaseModel):
    """Response model for generated dataset."""
    id: str
    datagen_event_id: Optional[str] = None
    project_id: Optional[str] = None
    job_id: Optional[str] = None
    dataset_name: Optional[str] = None
    generation_status: str
    dataset_size: Optional[int] = None
    complexity: Optional[int] = None
    coverage: Optional[int] = None
    total_samples: Optional[int] = None
    sample_groups: Optional[int] = None
    error_message: Optional[str] = None
    created_at: Optional[str] = None  # ISO format datetime string
    updated_at: Optional[str] = None  # ISO format datetime string
    completed_at: Optional[str] = None  # ISO format datetime string


class DatasetSampleResponse(BaseModel):
    """Response model for individual dataset sample."""
    id: str
    generated_dataset_id: str
    datagen_event_id: Optional[str] = None
    project_id: Optional[str] = None
    job_id: Optional[str] = None
    sample_type: str
    content: str
    label: Optional[str] = None
    sample_group: Optional[int] = None
    sample_index: Optional[int] = None
    created_at: Optional[str] = None  # ISO format datetime string
    updated_at: Optional[str] = None  # ISO format datetime string


class DatasetSamplesListResponse(BaseModel):
    """Response model for list of dataset samples."""
    samples: List[DatasetSampleResponse]
    total_count: int
    generated_dataset_id: Optional[str] = None
    datagen_event_id: Optional[str] = None
    project_id: Optional[str] = None


class GeneratedDatasetListResponse(BaseModel):
    """Response model for list of generated datasets."""
    datasets: List[GeneratedDatasetResponse]
    total_count: int


class GeneratedDatasetDetailResponse(BaseModel):
    """Response model for detailed generated dataset with content."""
    id: str
    datagen_event_id: Optional[str] = None
    project_id: Optional[str] = None
    job_id: Optional[str] = None
    dataset_name: Optional[str] = None
    generation_status: str
    dataset_content: Dict[str, Any]  # The actual generated dataset
    dataset_size: Optional[int] = None
    complexity: Optional[int] = None
    coverage: Optional[int] = None
    total_samples: Optional[int] = None
    sample_groups: Optional[int] = None
    error_message: Optional[str] = None
    created_at: Optional[str] = None  # ISO format datetime string
    updated_at: Optional[str] = None  # ISO format datetime string
    completed_at: Optional[str] = None  # ISO format datetime string


from sqlalchemy import Column, String, Integer, JSON, Text, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime, timezone
import uuid

Base = declarative_base()

class RainbowAnalysisJob(Base):
    __tablename__ = 'rainbow_analysis_jobs'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    status = Column(String(50), nullable=False, default='pending')
    prompts = Column(JSON, nullable=False)
    target_llm = Column(String(255), nullable=False)
    num_samples = Column(Integer, nullable=False)
    num_mutations = Column(Integer, nullable=False)
    max_iters = Column(Integer, nullable=False)
    api_key = Column(Text)
    base_url = Column(Text)
    nickname = Column(String(255))  # User nickname for Slack notifications
    project_id = Column(UUID(as_uuid=True), ForeignKey('projects.id'), nullable=True)  # Optional project association

    # Progress tracking
    current_iteration = Column(Integer, default=0)
    current_sample = Column(Integer, default=0)
    total_iterations = Column(Integer)
    
    # Results
    results = Column(JSON)
    error_message = Column(Text)
    
    # Metadata
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    completed_at = Column(DateTime)
    
    # Relationships
    project = relationship("Project", back_populates="jobs")
    datasets = relationship("Dataset", back_populates="job")
    checkpoints = relationship("RainbowCheckpoint", back_populates="job")
    celery_tasks = relationship("RainbowCeleryTask", back_populates="job")

class Project(Base):
    __tablename__ = 'projects'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    domain = Column(String(100), nullable=False)

    # Metadata
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Relationships
    jobs = relationship("RainbowAnalysisJob", back_populates="project")
    datasets = relationship("Dataset", back_populates="project")
    generated_datasets = relationship("GeneratedDataset", back_populates="project")

class RainbowCheckpoint(Base):
    __tablename__ = 'rainbow_checkpoints'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    job_id = Column(UUID(as_uuid=True), ForeignKey('rainbow_analysis_jobs.id'))
    iteration = Column(Integer, nullable=False)
    sample = Column(Integer, nullable=False)
    checkpoint_type = Column(String(50))
    data = Column(JSON)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    
    job = relationship("RainbowAnalysisJob", back_populates="checkpoints")

class RainbowCeleryTask(Base):
    __tablename__ = 'rainbow_celery_tasks'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    job_id = Column(UUID(as_uuid=True), ForeignKey('rainbow_analysis_jobs.id'))
    celery_task_id = Column(String(255), nullable=False)
    task_type = Column(String(50))
    status = Column(String(50))
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    
    job = relationship("RainbowAnalysisJob", back_populates="celery_tasks")

class Dataset(Base):
    __tablename__ = 'datasets'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    job_id = Column(UUID(as_uuid=True), ForeignKey('rainbow_analysis_jobs.id'), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey('projects.id'), nullable=True)
    status = Column(String(50), nullable=True)
    content = Column(JSON, nullable=False)
    label = Column(String(255), nullable=True)

    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    job = relationship("RainbowAnalysisJob", back_populates="datasets")
    project = relationship("Project", back_populates="datasets")


class DatagenEvent(Base):
    __tablename__ = 'datagen_events'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Form data fields from dataset generation workflow
    application_description = Column(Text, nullable=True)
    domain = Column(String(100), nullable=True)
    example_input = Column(Text, nullable=True)
    test_types = Column(JSON, nullable=True)  # Array of selected test types
    complexity = Column(Integer, nullable=True)  # Complexity slider value
    coverage = Column(Integer, nullable=True)  # Coverage percentage
    dataset_size = Column(Integer, nullable=True)  # Dataset size setting
    selected_experts = Column(JSON, nullable=True)  # Array of selected expert IDs/names
    uploaded_files = Column(JSON, nullable=True)  # Array of uploaded file references

    # Additional metadata
    user_session_id = Column(String(255), nullable=True)  # For tracking user sessions
    step_completed = Column(Integer, default=3)  # Which step was completed (should be 3)
    project_id = Column(UUID(as_uuid=True), ForeignKey('projects.id'), nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Relationships
    project = relationship("Project")
    generated_datasets = relationship("GeneratedDataset", back_populates="datagen_event")


class GeneratedDataset(Base):
    __tablename__ = 'generated_datasets'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Foreign key relationships
    datagen_event_id = Column(UUID(as_uuid=True), ForeignKey('datagen_events.id'), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey('projects.id'), nullable=True)
    job_id = Column(UUID(as_uuid=True), ForeignKey('rainbow_analysis_jobs.id'), nullable=True)  # Link to the generation job

    # Dataset metadata
    dataset_name = Column(String(255), nullable=True)  # Optional name for the dataset
    generation_status = Column(String(50), nullable=False, default='pending')  # pending, completed, failed

    # Dataset content - store the full samples.json structure
    dataset_content = Column(JSON, nullable=False)  # The complete generated dataset

    # Generation parameters (copied from datagen_event for convenience)
    dataset_size = Column(Integer, nullable=True)
    complexity = Column(Integer, nullable=True)
    coverage = Column(Integer, nullable=True)

    # Statistics about the generated dataset
    total_samples = Column(Integer, nullable=True)  # Total number of samples generated
    sample_groups = Column(Integer, nullable=True)  # Number of sample groups

    # Error information if generation failed
    error_message = Column(Text, nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    completed_at = Column(DateTime, nullable=True)  # When generation completed

    # Relationships
    datagen_event = relationship("DatagenEvent", back_populates="generated_datasets")
    project = relationship("Project")
    job = relationship("RainbowAnalysisJob")
    dataset_samples = relationship("DatasetSample", back_populates="generated_dataset")


class DatasetSample(Base):
    __tablename__ = 'dataset_samples'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Foreign key relationships
    generated_dataset_id = Column(UUID(as_uuid=True), ForeignKey('generated_datasets.id'), nullable=False)
    datagen_event_id = Column(UUID(as_uuid=True), ForeignKey('datagen_events.id'), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey('projects.id'), nullable=True)
    job_id = Column(UUID(as_uuid=True), ForeignKey('rainbow_analysis_jobs.id'), nullable=True)

    # Sample data
    sample_type = Column(String(50), nullable=False)  # e.g., "content", "question-answer"
    content = Column(Text, nullable=False)  # The actual content/text of the sample
    label = Column(String(255), nullable=True)  # The label/category for the sample

    # Sample metadata
    sample_group = Column(Integer, nullable=True)  # Which group this sample belongs to (if applicable)
    sample_index = Column(Integer, nullable=True)  # Index within the group or overall

    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Relationships
    generated_dataset = relationship("GeneratedDataset", back_populates="dataset_samples")
    datagen_event = relationship("DatagenEvent")
    project = relationship("Project")
    job = relationship("RainbowAnalysisJob")